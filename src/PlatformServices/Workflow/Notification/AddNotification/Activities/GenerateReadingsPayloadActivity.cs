using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Observability.Logs;
using Domain.Enums;
using Domain.Extensions;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Application.Apis.Clients.Extensions.UsersExtensions;
using static Workflow.Constants.NotificationWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Notification.AddNotification.Activities
{
    public class GenerateReadingsPayloadActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<GenerateReadingsPayloadActivity> _logger;

        public GenerateReadingsPayloadActivity(
            IClientsApiService clientsApiService,
            ILogger<GenerateReadingsPayloadActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var message = context.GetTransientVariable<CreateNotification>(Variables.Message);

                var readingResponse = await _clientsApiService.GetReadingById(message.Id);

                if (readingResponse == null && message.Theme != NotificationTheme.ReadingDeleted)
                {
                    _logger.LogReadingNotFound();
                    return Fault(new Exception("Leitura não encontrada"));
                }

                var instrumentId = readingResponse != null ? readingResponse.Instrument.Id : Guid.Empty;

                if (message.AuxiliaryData != null && message.AuxiliaryData.TryGetValue("InstrumentId", out var instrumentIdAux))
                {
                    if (Guid.TryParse(instrumentIdAux, out Guid ParsedInstrumentId))
                    {
                        instrumentId = ParsedInstrumentId;
                    }
                    else
                    {
                        _logger.LogInvalidInstrument();
                        return Fault(new Exception("Instrumento inválido"));
                    }
                }

                var instrumentResponse = await _clientsApiService.GetInstrumentById(instrumentId);

                if (instrumentResponse == null)
                {
                    _logger.LogInstrumentNotFoundForReading();
                    return Fault(new Exception("Instrumento não encontrado"));
                }

                var users = await _clientsApiService.GetUsersAsync(
                    GetListUserRequestForNotification(instrumentResponse.Structure.Id));

                if (users == null || !users.Any())
                {
                    return Done();
                }

                if (message.Theme != NotificationTheme.ReadingDeleted && readingResponse.Values == null && !readingResponse.Values.Any())
                {
                    _logger.LogReadingValuesNotFound();
                    return Fault(new Exception("Leitura não encontrada"));
                }

                var readingDate = readingResponse != null ? readingResponse.Values.Max(x => x.Date) : DateTime.Now;

                if (message.AuxiliaryData != null && message.AuxiliaryData.TryGetValue("ReadingDate", out var DateAux))
                {
                    if (DateTime.TryParse(DateAux, out DateTime parsedDate))
                    {
                        readingDate = parsedDate;
                    }
                    else
                    {
                        _logger.LogInvalidReadingDate();
                        return Fault(new Exception("Data de leitura inválida"));
                    }
                }

                var userId = Guid.Empty;

                if (message.CreatedById != Guid.Empty && message.CreatedById != null)
                {
                    userId = (Guid)message.CreatedById;
                }

                var modifiedBy = await _clientsApiService.GetUserById(userId);

                var modifiedByFullName = modifiedBy != null
                    ? modifiedBy.FirstName + " " + modifiedBy.Surname
                    : UnknownUser;

                var notificationMessage = String.Format(
                    message.GetReadingsMessageByAction(),
                    instrumentResponse.Identifier,
                    instrumentResponse.Structure.Name,
                    readingDate.ToString("dd/MM/yyyy HH:mm:ss"),
                    modifiedByFullName,
                    readingResponse?.Id.ToString()
                    );

                context.SetTransientVariable(
                    Variables.Notification,
                    new List<AddNotificationRequest>()
                    {
                        new AddNotificationRequest
                        {
                            Users = users.Select(x => x.Id).ToList(),
                            NotificationTheme = message.Theme,
                            NotificationMessage = notificationMessage
                        }
                    });

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogGenerateReadingsPayloadUnexpectedError(e.Message, e);
                return Fault(e);
            }
        }
    }
}
