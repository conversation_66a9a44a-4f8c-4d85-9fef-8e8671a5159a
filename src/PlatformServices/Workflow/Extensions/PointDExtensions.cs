using Domain.Enums;
using Geometry.Core;

namespace Workflow.Extensions;

public static class PointDExtensions
{
    private const double EdgeProximityTolerance = 0.5;

    /// <summary>
    /// Gets the X coordinate of the specified edge (leftmost or rightmost) from a collection of points.
    /// </summary>
    /// <param name="externalPoints">The collection of points to analyze.</param>
    /// <param name="edge">The edge to find - either <see cref="XAxisEdge.Left"/> for minimum X coordinate
    /// or <see cref="XAxisEdge.Right"/> for maximum X coordinate.</param>
    /// <returns>
    /// The X coordinate of the specified edge:
    /// - For <see cref="XAxisEdge.Left"/>: returns the minimum X coordinate from all points
    /// - For <see cref="XAxisEdge.Right"/>: returns the maximum X coordinate from all points
    /// </returns>
    /// <exception cref="ArgumentOutOfRangeException">
    /// Thrown when <paramref name="edge"/> is not a valid <see cref="XAxisEdge"/> value.
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when <paramref name="externalPoints"/> is empty.
    /// </exception>
    public static double GetEdgeOnXAxis(
        this List<PointD> externalPoints,
        XAxisEdge edge)
    {
        return edge switch
        {
            XAxisEdge.Left => externalPoints.Min(point => point.X),
            XAxisEdge.Right => externalPoints.Max(point => point.X),
            _ => throw new ArgumentOutOfRangeException(nameof(edge), edge, null)
        };
    }

    /// <summary>
    /// Determines whether a point is close to the specified edge of a polygon defined by a collection of points.
    /// </summary>
    /// <param name="externalPoints">The collection of points that define the polygon boundary.</param>
    /// <param name="point">The point to check for proximity to the edge.</param>
    /// <param name="edge">The edge to check against - either <see cref="XAxisEdge.Left"/> or <see cref="XAxisEdge.Right"/>.</param>
    /// <returns>
    /// <c>true</c> if the point's X coordinate is within <see cref="EdgeProximityTolerance"/> (0.5 units)
    /// of the specified edge; otherwise, <c>false</c>.
    /// </returns>
    /// <exception cref="ArgumentOutOfRangeException">
    /// Thrown when <paramref name="edge"/> is not a valid <see cref="XAxisEdge"/> value.
    /// </exception>
    /// <exception cref="InvalidOperationException">
    /// Thrown when <paramref name="externalPoints"/> is empty.
    /// </exception>
    public static bool IsCloseToEdge(
        this List<PointD> externalPoints,
        PointD point,
        XAxisEdge edge)
    {
        var xCoordinateOfEdge = externalPoints.GetEdgeOnXAxis(edge);

        return Math.Abs(point.X - xCoordinateOfEdge) < EdgeProximityTolerance;
    }

    /// <summary>
    /// Determines whether a line segment between two points is nearly vertical.
    /// </summary>
    /// <param name="point1">The first point of the line segment.</param>
    /// <param name="point2">The second point of the line segment.</param>
    /// <returns>
    /// <c>true</c> if the line segment is nearly vertical (angle between 88° and 92° from horizontal)
    /// or perfectly vertical; otherwise, <c>false</c>.
    /// </returns>
    public static bool SegmentIsNearlyVertical(
        this PointD point1,
        PointD point2)
    {
        var deltaX = Math.Abs(point2.X - point1.X);
        var deltaY = Math.Abs(point2.Y - point1.Y);

        // Avoid division by zero
        if (deltaX < 1e-10)
        {
            return true; // Perfectly vertical
        }

        // Calculate angle from horizontal in degrees
        var angleRadians = Math.Atan(deltaY / deltaX);
        var angleDegrees = angleRadians * 180.0 / Math.PI;

        // Check if angle is between 88 and 92 degrees (nearly vertical)
        return angleDegrees is >= 88.0 and <= 92.0;
    }

    /// <summary>
    /// Gets the leftmost point from a polygon while excluding points that are part of nearly vertical line segments.
    /// </summary>
    /// <param name="externalPoints">The collection of points that define the polygon boundary,
    /// assumed to be in sequential order around the polygon perimeter.</param>
    /// <returns>
    /// The leftmost valid point that is not part of a nearly vertical line segment.
    /// If no such point exists, returns the leftmost point ordered by X coordinate ascending,
    /// then by Y coordinate descending.
    /// </returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown when <paramref name="externalPoints"/> is empty.
    /// </exception>
    public static PointD GetLeftmostPointExcludingVerticalLines(
        this List<PointD> externalPoints)
    {
        var minX = externalPoints.Min(p => p.X);

        // Get vertices near the leftmost part of the polygon
        var leftmostVertices = externalPoints
            .Where(point => Math.Abs(point.X - minX) < EdgeProximityTolerance)
            .ToList();

        if (leftmostVertices.Count <= 1)
        {
            return leftmostVertices.FirstOrDefault();
        }

        var validVertices = new List<PointD>();

        for (int i = 0; i < externalPoints.Count; i++)
        {
            var currentVertex = externalPoints[i];

            // Only process vertices near the rightmost part
            if (Math.Abs(currentVertex.X - minX) >= EdgeProximityTolerance)
            {
                continue;
            }

            var prevVertex = externalPoints[(i - 1 + externalPoints.Count) %
                                            externalPoints.Count];

            // Check if either adjacent segment is nearly vertical (88-92 degrees from horizontal)
            var isPrevSegmentVertical =
                prevVertex.SegmentIsNearlyVertical(currentVertex);

            // If neither adjacent segment is nearly vertical, include this vertex
            if (!isPrevSegmentVertical)
            {
                validVertices.Add(currentVertex);
            }
        }

        // If no valid vertices found, fall back to the vertex with minimum X
        if (!validVertices.Any())
        {
            return externalPoints.OrderBy(p => p.X)
                .ThenByDescending(p => p.Y)
                .First();
        }

        // Return the vertex with maximum X from valid vertices
        return validVertices.OrderBy(p => p.X)
            .ThenByDescending(p => p.Y)
            .First();
    }

    /// <summary>
    /// Gets the rightmost point from a polygon while excluding points that are part of nearly vertical line segments.
    /// </summary>
    /// <param name="externalPoints">The collection of points that define the polygon boundary,
    /// assumed to be in sequential order around the polygon perimeter.</param>
    /// <returns>
    /// The rightmost valid point that is not part of a nearly vertical line segment.
    /// If no such point exists, returns the rightmost point ordered by X coordinate descending,
    /// then by Y coordinate descending.
    /// </returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown when <paramref name="externalPoints"/> is empty.
    /// </exception>
    public static PointD GetRightmostPointExcludingVerticalLines(
        this List<PointD> externalPoints)
    {
        var maxX = externalPoints.Max(p => p.X);

        // Get vertices near the rightmost part of the polygon
        var rightmostVertices = externalPoints
            .Where(p => Math.Abs(p.X - maxX) < EdgeProximityTolerance)
            .ToList();

        if (rightmostVertices.Count <= 1)
        {
            return rightmostVertices.FirstOrDefault();
        }

        var validVertices = new List<PointD>();

        for (int i = 0; i < externalPoints.Count; i++)
        {
            var currentVertex = externalPoints[i];

            // Only process vertices near the rightmost part
            if (Math.Abs(currentVertex.X - maxX) >= EdgeProximityTolerance)
            {
                continue;
            }

            var nextVertex = externalPoints[(i + 1) % externalPoints.Count];

            // Check if either adjacent segment is nearly vertical (88-92 degrees from horizontal)
            bool isNextSegmentVertical =
                currentVertex.SegmentIsNearlyVertical(nextVertex);

            // If neither adjacent segment is nearly vertical, include this vertex
            if (!isNextSegmentVertical)
            {
                validVertices.Add(currentVertex);
            }
        }

        // If no valid vertices found, fall back to the vertex with maximum X
        if (!validVertices.Any())
        {
            return externalPoints.OrderByDescending(p => p.X)
                .ThenByDescending(p => p.Y)
                .First();
        }

        // Return the vertex with maximum X from valid vertices
        return validVertices.OrderByDescending(p => p.X)
            .ThenByDescending(p => p.Y)
            .First();
    }

    /// <summary>
    /// Finds the corresponding point on the external polygon boundary for a given slope limit point.
    /// </summary>
    /// <param name="externalPoints">The collection of points that define the external polygon boundary.</param>
    /// <param name="slopeLimit">The slope limit point for which to find the corresponding external boundary point.</param>
    /// <returns>
    /// A point on the external polygon boundary that corresponds to the slope limit
    /// </returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown when <paramref name="externalPoints"/> is empty.
    /// </exception>
    /// <exception cref="ArgumentOutOfRangeException">
    /// Thrown when edge detection fails due to invalid enum values.
    /// </exception>
    public static PointD FindSlopeLimitPointOnExternal(
        this List<PointD> externalPoints,
        PointD slopeLimit)
    {
        var validLeftmostPoint = externalPoints
            .GetLeftmostPointExcludingVerticalLines();

        var validRightmostPoint = externalPoints
            .GetRightmostPointExcludingVerticalLines();

        if (externalPoints.IsCloseToEdge(slopeLimit, XAxisEdge.Left) ||
            slopeLimit.X < validLeftmostPoint.X)
        {
            return new PointD(validLeftmostPoint.X, slopeLimit.Y);
        }

        if (externalPoints.IsCloseToEdge(slopeLimit, XAxisEdge.Right) ||
            slopeLimit.X > validRightmostPoint.X)
        {
            return new PointD(validRightmostPoint.X, slopeLimit.Y);
        }

        return new PointD(
            slopeLimit.X,
            Helper.FindYOnPolygonEdge(slopeLimit.X, externalPoints));
    }

    /// <summary>
    /// Validates whether both slope limit points fall within the horizontal bounds of the external polygon.
    /// </summary>
    /// <param name="externalPoints">The collection of points that define the external polygon boundary.</param>
    /// <param name="slopeLimitStart">The starting point of the slope limit to validate.</param>
    /// <param name="slopeLimitEnd">The ending point of the slope limit to validate.</param>
    /// <returns>
    /// <c>true</c> if both slope limit points have X coordinates that fall within the minimum and maximum
    /// X coordinates of the external polygon; otherwise, <c>false</c>.
    /// </returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown when <paramref name="externalPoints"/> is empty.
    /// </exception>
    public static bool SlopeLimitsAreValid(
        this List<PointD> externalPoints,
        PointD slopeLimitStart,
        PointD slopeLimitEnd)
    {
        var minX = externalPoints.Min(point => point.X);
        var maxX = externalPoints.Max(point => point.X);

        return slopeLimitStart.X >= minX && slopeLimitStart.X <= maxX &&
               slopeLimitEnd.X >= minX && slopeLimitEnd.X <= maxX;
    }

    public static List<PointD> NormalizeVerticalEdges(this List<PointD> externalPoints)
    {
        return externalPoints
            .NormalizeVerticalLeftEdge()
            .NormalizeVerticalRightEdge();
    }
    
    private static List<PointD> NormalizeVerticalRightEdge(
        this List<PointD> externalPoints)
    {
        externalPoints.ForEach(point => point.ApplyRounding());

        var rightmostValidPoint = externalPoints
            .GetRightmostPointExcludingVerticalLines();

        var invalidPointsOnEdge = externalPoints
            .Where(point => point.X > rightmostValidPoint.X)
            .OrderBy(point => point.X)
            .ToArray();

        if (!invalidPointsOnEdge.Any())
        {
            return externalPoints;
        }

        var maxX = invalidPointsOnEdge.Min(point => point.X);
        var minY = invalidPointsOnEdge.Min(point => point.Y);

        var rightmostBottomPoint = new PointD(maxX, minY);
        var rightmostTopPoint = new PointD(maxX, rightmostValidPoint.Y);

        if (externalPoints.All(point => point != rightmostBottomPoint))
        {
            externalPoints.Add(rightmostBottomPoint);
        }
        if (externalPoints.All(point => point != rightmostTopPoint))
        {
            externalPoints.Add(rightmostTopPoint);
        }

        return externalPoints;
    }
    
    private static List<PointD> NormalizeVerticalLeftEdge(
        this List<PointD> externalPoints)
    {
        externalPoints.ForEach(point => point.ApplyRounding());

        var leftmostValidPoint = externalPoints
            .GetLeftmostPointExcludingVerticalLines();

        var invalidPointsOnEdge = externalPoints
            .Where(point => point.X < leftmostValidPoint.X)
            .OrderBy(point => point.X)
            .ToArray();

        if (!invalidPointsOnEdge.Any())
        {
            return externalPoints;
        }

        var minX = invalidPointsOnEdge.Min(point => point.X);
        var minY = invalidPointsOnEdge.Min(point => point.Y);

        var leftmostBottomPoint = new PointD(minX, minY);
        var leftmostTopPoint = new PointD(minX, leftmostValidPoint.Y);

        if (externalPoints.All(point => point != leftmostBottomPoint))
        {
            externalPoints.Add(leftmostBottomPoint);
        }
        if (externalPoints.All(point => point != leftmostTopPoint))
        {
            externalPoints.Add(leftmostTopPoint);
        }

        return externalPoints;
    }
}
